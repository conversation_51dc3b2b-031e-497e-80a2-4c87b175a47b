#!/usr/bin/env python3
"""
Test file để kiểm tra UI mới của Step 2 trong raw_data_ui.py
"""

import sys
import os

# Thêm thư mục hiện tại vào Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from raw_data_ui import RawDataUI

def main():
    """Hàm chính để test UI mới"""
    try:
        app = QApplication(sys.argv)
        
        # Tạo UI
        ui = RawDataUI()
        ui.show()
        
        # Chuyển trực tiếp đến tab "Xử Lý Dữ Liệu" (tab index 1)
        ui.tabs.setCurrentIndex(1)
        
        # Thiết lập một số dữ liệu test
        ui.process_spreadsheet_input.setText("https://docs.google.com/spreadsheets/d/test_spreadsheet_id/edit")
        
        # Thiết lập headers test
        test_headers = [
            "STT", "Mã Deal", "Tên Deal",  # 3 header đầu sẽ bị bỏ qua
            "<PERSON><PERSON><PERSON> gốc", "<PERSON><PERSON><PERSON> bán", "Mô tả sản phẩm", 
            "Link sản phẩm", "Số lượng", "Trạng thái",
            "Ghi chú", "Ngày tạo", "Người tạo"
        ]
        ui.available_headers = test_headers[3:]  # Bỏ qua 3 header đầu
        
        # Enable các nút
        ui.add_rule_btn.setEnabled(True)
        ui.load_headers_btn.setEnabled(True)
        
        # Enable rule mặc định nếu tồn tại
        if ui.processing_rules:
            for rule_widget in ui.processing_rules:
                rule_widget.setEnabled(True)
        
        ui.log(f"Test UI loaded successfully!")
        ui.log(f"Loaded {len(ui.available_headers)} test headers (bỏ qua 3 header đầu)")
        ui.log("Bạn có thể test các tính năng:")
        ui.log("1. Click 'Chọn Headers' để mở dialog chọn headers")
        ui.log("2. Click '+ Thêm Quy Tắc Xử Lý' để thêm rule mới")
        ui.log("3. Chọn điều kiện từ dropdown")
        
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"Error running test: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
