#!/usr/bin/env python3
"""
Script test để kiểm tra import và các phương thức
"""

try:
    from raw_data_ui import RawDataUI
    print("✅ Import RawDataUI thành công")
    
    # Tạo instance để kiểm tra
    ui = RawDataUI()
    print("✅ Tạo instance RawDataUI thành công")
    
    # Kiểm tra các phương thức quan trọng
    methods_to_check = [
        'load_process_sheets_and_headers',
        'load_headers_from_selected_sheet',
        'start_import',
        'start_processing'
    ]
    
    for method_name in methods_to_check:
        if hasattr(ui, method_name):
            print(f"✅ Phương thức {method_name} tồn tại")
        else:
            print(f"❌ Phương thức {method_name} KHÔNG tồn tại")
    
    # Kiểm tra phương thức cũ không còn tồn tại
    if hasattr(ui, 'load_process_sheets'):
        print("❌ Phương thức cũ load_process_sheets vẫn còn tồn tại (cần xóa)")
    else:
        print("✅ Phương thức cũ load_process_sheets đã được xóa")
        
    print("\n🎉 Tất cả kiểm tra đã hoàn thành!")
    
except ImportError as e:
    print(f"❌ Lỗi import: {e}")
except Exception as e:
    print(f"❌ Lỗi khác: {e}")
